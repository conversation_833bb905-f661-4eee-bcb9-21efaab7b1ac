# 🩺 Medical Voice Analyzer

A professional AI-powered medical conversation analysis system with dual-mode functionality for comprehensive audio recording and real-time voice-to-text processing.

## ✨ Features

### 🎙️ Speech Recording Mode
- High-quality audio recording (44.1kHz, 16-bit)
- Automatic MP3 compression for easy storage
- Real-time duration tracking
- Instant download after recording
- Perfect for medical consultations and meetings

### 📝 Voice-to-Text Analysis Mode
- Real-time speech-to-text transcription
- AI-powered speaker identification (Doctor vs <PERSON><PERSON>)
- Conversation timeline with separate speaker tracks
- Medical conversation summaries using AI
- Live word display during recording

## 🏗️ Project Structure

```
medical-voice-analyzer/
├── backend/
│   ├── main.py              # FastAPI backend server
│   ├── groq_whisper.py      # Whisper transcription module
│   ├── requirements.txt     # Backend dependencies
│   └── .env                 # Environment variables (create this)
├── frontend/
│   ├── app.py              # Main Streamlit application
│   ├── speech_recording_page.py    # Speech recording interface
│   ├── voice_to_text_page.py       # Voice-to-text interface
│   └── requirements.txt     # Frontend dependencies
├── recordings/              # Saved audio recordings (auto-created)
├── run_app.py              # Easy startup script
└── README.md               # This file
```

## 🚀 Quick Start

### 1. Install Dependencies

```bash
# Backend dependencies
pip install -r backend/requirements.txt

# Frontend dependencies
pip install -r frontend/requirements.txt
```

### 2. Setup Environment

Create `backend/.env` file with your API keys:

```env
GROQ_API_KEY=your_groq_api_key_here
```

### 3. Run the Application

**Option A: Easy Startup (Recommended)**
```bash
python run_app.py
```

**Option B: Manual Startup**
```bash
# Terminal 1: Start Backend
cd backend
uvicorn main:app --host 0.0.0.0 --port 8000 --reload

# Terminal 2: Start Frontend
cd frontend
streamlit run app.py --server.port 8501
```

### 4. Access the Application

- **Frontend**: http://localhost:8501
- **Backend API**: http://localhost:8000
- **API Documentation**: http://localhost:8000/docs

## 🎯 How to Use

### Speech Recording Mode
1. Click "🎙️ Speech Recording Mode" on the landing page
2. Click "Start Recording" to begin audio capture
3. Speak your medical conversation
4. Click "Stop Recording" when finished
5. Click "Download MP3" to save the high-quality audio file

### Voice-to-Text Analysis Mode
1. Click "📝 Voice-to-Text Mode" on the landing page
2. Click "Start" to begin recording and real-time transcription
3. Speak your medical conversation (system will identify doctor vs patient)
4. Click "Stop" when finished
5. Review the conversation timeline with separated speakers
6. Click "Generate Summary" for AI-powered medical summary

## 🔧 API Endpoints

### Voice-to-Text Endpoints
- `POST /analyze-conversation` - Analyze audio and separate speakers
- `POST /generate-summary` - Generate AI summary of conversation

### Speech Recording Endpoints
- `POST /speech-recording/save` - Save audio recording as MP3
- `GET /speech-recording/download/{filename}` - Download saved recording
- `GET /speech-recording/list` - List all saved recordings

## 🛠️ Technical Details

### Backend Technologies
- **FastAPI** - Modern Python web framework
- **Groq Whisper** - High-speed speech-to-text
- **LLaMA 3.1** - AI model for speaker identification and summarization
- **PyDub** - Audio processing and format conversion

### Frontend Technologies
- **Streamlit** - Python web app framework
- **HTML/JavaScript** - Real-time audio recording
- **Web Audio API** - Browser-based audio capture

### AI Features
- **Speaker Diarization** - Automatic doctor/patient identification
- **Medical Context Understanding** - AI trained on medical conversation patterns
- **Real-time Processing** - Live transcription with minimal delay
- **Summary Generation** - Structured medical conversation summaries

## 📋 Requirements

### System Requirements
- Python 3.8+
- Modern web browser with microphone access
- Internet connection for AI processing

### API Keys Required
- **Groq API Key** - For Whisper transcription and LLaMA processing
  - Get yours at: https://console.groq.com/

## 🔒 Privacy & Security

- Audio processing happens locally and via secure API calls
- No audio data is permanently stored on external servers
- Recordings are saved locally in the `recordings/` directory
- All API communications use HTTPS when deployed

## 🐛 Troubleshooting

### Common Issues

**Microphone Access Denied**
- Ensure browser has microphone permissions
- Check system microphone settings
- Try refreshing the page

**Backend Connection Error**
- Verify backend server is running on port 8000
- Check if GROQ_API_KEY is set in .env file
- Ensure all dependencies are installed

**Audio Quality Issues**
- Use a quiet environment
- Position microphone 6-12 inches from speakers
- Ensure stable internet connection

## 🤝 Contributing

This is a professional medical application. For improvements or bug reports, please ensure all changes maintain HIPAA compliance and medical data security standards.

## 📄 License

This project is for educational and professional medical use. Please ensure compliance with local healthcare data regulations.

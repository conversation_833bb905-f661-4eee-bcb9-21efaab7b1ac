import os
import logging
from datetime import datetime
from dotenv import load_dotenv
from fastapi import FastAPI, File, UploadFile
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from groq_whisper import transcribe_audio
from pydub import AudioSegment
import json

# Setup simple logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(message)s')
logger = logging.getLogger(__name__)

# Load environment variables from .env file
load_dotenv()

app = FastAPI()

# Allow CORS for local Streamlit frontend
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

print(" Medical Voice Chat Backend Started!")
print(" Server running on: http://localhost:8000")
print(" Available endpoints:")
print("   - GET  /                      : Health check")
print("   - POST /transcribe            : Simple transcription")
print("   - POST /analyze-conversation  : AI conversation analysis")
print("   - POST /summarize-conversation: Generate conversation summary")
print("=" * 50)

@app.get("/")
def root():
    print(" [HEALTH] Health check requested")
    return {"message": "Voice-to-text API running", "status": "healthy", "timestamp": datetime.now().isoformat()}

@app.post("/transcribe/")
async def transcribe(file: UploadFile = File(...)):
    print(f" [TRANSCRIBE] Received file: {file.filename}")

    temp_path = f"temp_{file.filename}"
    with open(temp_path, "wb") as f:
        f.write(await file.read())

    print(f" [TRANSCRIBE] File saved temporarily: {temp_path}")

    try:
        print("🎤 [TRANSCRIBE] Starting Groq Whisper transcription...")
        transcript = transcribe_audio(temp_path)
        print(f" [TRANSCRIBE] Success! Transcript: {transcript[:100]}...")

        result = {"transcript": transcript}
        os.remove(temp_path)
        print(" [TRANSCRIBE] Temporary file cleaned up")
        return JSONResponse(result)
    except Exception as e:
        print(f" [TRANSCRIBE] Error: {str(e)}")
        if os.path.exists(temp_path):
            os.remove(temp_path)
        return JSONResponse({
            "error": str(e),
            "transcript": "",
        }, status_code=500)

@app.post("/analyze-conversation/")
async def analyze_conversation(file: UploadFile = File(...)):
    print(f" [ANALYZE] Received conversation file: {file.filename}")

    temp_path = f"temp_conversation_{file.filename}"
    with open(temp_path, "wb") as f:
        f.write(await file.read())

    print(f" [ANALYZE] File saved temporarily: {temp_path}")

    try:
        print(" [ANALYZE] Starting Groq Whisper transcription...")
        full_transcript = transcribe_audio(temp_path)
        print(f" [ANALYZE] Full transcript obtained: {full_transcript[:100]}...")
        print(" [ANALYZE] Sending to LLM for speaker analysis...")
        analyzed_conversation = await analyze_speakers_with_llm(full_transcript)

        result = {
            "transcript": full_transcript,
            "doctor_transcript": analyzed_conversation.get("doctor_parts", ""),
            "patient_transcript": analyzed_conversation.get("patient_parts", ""),
            "full_conversation": analyzed_conversation.get("timeline", []),
            "analysis_confidence": analyzed_conversation.get("confidence", 0.8)
        }

        os.remove(temp_path)
        print(" [ANALYZE] Temporary file cleaned up")
        return JSONResponse(result)

    except Exception as e:
        print(f" [ANALYZE] Error: {str(e)}")
        if os.path.exists(temp_path):
            os.remove(temp_path)
        return JSONResponse({
            "error": str(e),
            "transcript": "",
            "doctor_transcript": "",
            "patient_transcript": "",
            "full_conversation": []
        }, status_code=500)

async def analyze_speakers_with_llm(transcript):

    try:
        from groq import Groq
        client = Groq(api_key=os.getenv("GROQ_API_KEY"))

        prompt = f"""
You are an expert medical conversation analyst. Analyze this conversation transcript and identify complete sentences or thoughts for each speaker. DO NOT break sentences in the middle.

TRANSCRIPT:
{transcript}

ANALYSIS RULES:
1. Identify COMPLETE sentences or thoughts for each speaker
2. DOCTOR typically: Uses medical terminology, asks diagnostic questions, gives advice/treatment, introduces themselves
3. PATIENT typically: Describes symptoms, asks questions, expresses concerns, responds to doctor's questions
4. Keep complete sentences together - do NOT split a sentence between speakers
5. A speaker can have multiple consecutive sentences if they are continuing their thought
6. Look for natural conversation flow and context clues
7. If unsure about speaker, use context: medical questions = doctor, symptom descriptions = patient

Return output in this exact JSON format:
{{
    "doctor_parts": "...",
    "patient_parts": "...",
    "timeline": [
        {{"speaker": "doctor", "text": "Complete sentence or thought here", "timestamp": "estimated"}},
        {{"speaker": "patient", "text": "Complete sentence or thought here", "timestamp": "estimated"}}
    ],
    "confidence": 0.9
}}
"""

        print(" [LLM] Sending conversation to Groq for analysis...")
        response = client.chat.completions.create(
            model="llama-3.1-8b-instant",
            messages=[
                {
                    "role": "system",
                    "content": (
                        "You are a medical conversation analysis expert. "
                        "Always respond with valid JSON only. NEVER assign two consecutive lines to the same speaker unless it's clearly a follow-up."
                    )
                },
                {"role": "user", "content": prompt}
            ],
            temperature=0.1,
            max_tokens=2000
        )

        llm_response = response.choices[0].message.content.strip()
        print(f" [LLM] Received analysis: {llm_response[:200]}...")

        try:
            analysis = json.loads(llm_response)
            print(" [LLM] Successfully parsed JSON response")

            # Clean timeline - remove empty entries and ensure proper formatting
            cleaned_timeline = []
            for item in analysis.get("timeline", []):
                speaker = item.get("speaker")
                text = item.get("text", "").strip()

                # Only add non-empty text entries
                if text:
                    cleaned_timeline.append({
                        "speaker": speaker,
                        "text": text,
                        "timestamp": item.get("timestamp", "estimated")
                    })

            analysis["timeline"] = cleaned_timeline
            return analysis

        except json.JSONDecodeError:
            print(" [LLM] JSON parsing failed")
            return {
                "doctor_parts": "Analysis failed",
                "patient_parts": "Analysis failed",
                "timeline": [],
                "confidence": 0.3
            }

    except Exception as e:
        print(f" [LLM] Analysis error: {e}")
        return {
            "doctor_parts": f"LLM Analysis Error: {str(e)}",
            "patient_parts": f"LLM Analysis Error: {str(e)}",
            "timeline": [],
            "confidence": 0.1
        }

@app.post("/generate-summary")
async def generate_summary(data: dict):
    return await summarize_conversation(data)

@app.post("/summarize-conversation/")
async def summarize_conversation(data: dict):
    try:
        transcript = data.get("transcript", "")
        timeline = data.get("timeline", [])
        if timeline and isinstance(timeline, list):
            conversation_text = " ".join([seg.get("text", "") for seg in timeline])
        else:
            conversation_text = transcript

        from groq import Groq
        client = Groq(api_key=os.getenv("GROQ_API_KEY"))

        prompt = f"""
You are a medical conversation summarizer. Read the following conversation and provide a concise, clear summary of the main points, symptoms, diagnosis, and advice given. Use simple language.

CONVERSATION:
{conversation_text}

SUMMARY:
"""

        response = client.chat.completions.create(
            model="llama-3.1-8b-instant",
            messages=[
                {"role": "system", "content": "You are a medical conversation summarizer. Always respond with a clear summary only."},
                {"role": "user", "content": prompt}
            ],
            temperature=0.2,
            max_tokens=400
        )

        summary = response.choices[0].message.content.strip()
        return {"summary": summary}
    except Exception as e:
        return {"error": str(e), "summary": ""}

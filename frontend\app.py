import streamlit as st
import requests
import time
import json
from io import BytesIO

# Page configuration
st.set_page_config(
    page_title="Medical Voice Analyzer",
    page_icon="🩺",
    layout="wide",
    initial_sidebar_state="collapsed"
)

# Initialize session state for navigation
if 'page' not in st.session_state:
    st.session_state.page = 'landing'
if 'selected_mode' not in st.session_state:
    st.session_state.selected_mode = None

# Custom CSS for beautiful black theme
st.markdown("""
<style>
    /* Global Styles */
    .stApp {
        background: linear-gradient(135deg, #0f0f0f 0%, #1a1a1a 100%);
        color: #e0e0e0;
    }
    
    /* Hide Streamlit elements */
    #MainMenu {visibility: hidden;}
    footer {visibility: hidden;}
    header {visibility: hidden;}
    
    /* Landing Page Styles */
    .landing-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        min-height: 80vh;
        text-align: center;
        padding: 2rem;
    }
    
    .app-title {
        font-size: 3.5rem;
        font-weight: 700;
        background: linear-gradient(135deg, #64b5f6 0%, #90caf9 50%, #bbdefb 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        margin-bottom: 1rem;
        text-shadow: 0 0 30px rgba(100, 181, 246, 0.3);
    }
    
    .app-subtitle {
        font-size: 1.3rem;
        color: #b0bec5;
        margin-bottom: 3rem;
        max-width: 600px;
        line-height: 1.6;
    }
    
    .mode-buttons-container {
        display: flex;
        gap: 3rem;
        margin-top: 2rem;
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .mode-button {
        background: linear-gradient(135deg, #263238 0%, #37474f 100%);
        border: 2px solid #546e7a;
        border-radius: 20px;
        padding: 2.5rem;
        width: 300px;
        cursor: pointer;
        transition: all 0.3s ease;
        text-decoration: none;
        color: #e0e0e0;
        box-shadow: 0 8px 25px rgba(0,0,0,0.3);
        backdrop-filter: blur(10px);
    }
    
    .mode-button:hover {
        transform: translateY(-10px);
        box-shadow: 0 15px 35px rgba(100, 181, 246, 0.2);
        border-color: #64b5f6;
        background: linear-gradient(135deg, #37474f 0%, #455a64 100%);
    }
    
    .mode-icon {
        font-size: 3rem;
        margin-bottom: 1rem;
        display: block;
    }
    
    .mode-title {
        font-size: 1.5rem;
        font-weight: 600;
        margin-bottom: 0.5rem;
        color: #90caf9;
    }
    
    .mode-description {
        font-size: 1rem;
        color: #b0bec5;
        line-height: 1.4;
    }
    
    /* Back button */
    .back-button {
        position: fixed;
        top: 20px;
        left: 20px;
        background: rgba(38, 50, 56, 0.8);
        border: 1px solid #546e7a;
        border-radius: 10px;
        padding: 10px 20px;
        color: #90caf9;
        cursor: pointer;
        transition: all 0.3s ease;
        backdrop-filter: blur(10px);
    }
    
    .back-button:hover {
        background: rgba(55, 71, 79, 0.9);
        border-color: #64b5f6;
    }
</style>
""", unsafe_allow_html=True)

def show_landing_page():
    """Display the landing page with mode selection"""
    st.markdown("""
    <div class="landing-container">
        <h1 class="app-title">🩺 Medical Voice Analyzer</h1>
        <p class="app-subtitle">
            Advanced AI-powered medical conversation analysis system with dual-mode functionality
            for comprehensive audio recording and real-time voice-to-text processing
        </p>
        
        <div class="mode-buttons-container">
            <div class="mode-button" onclick="selectMode('speech')">
                <span class="mode-icon">🎙️</span>
                <div class="mode-title">Speech Recording</div>
                <div class="mode-description">
                    Record high-quality audio conversations and save as MP3 files for future analysis and listening
                </div>
            </div>
            
            <div class="mode-button" onclick="selectMode('voice_to_text')">
                <span class="mode-icon">📝</span>
                <div class="mode-title">Voice-to-Text Analysis</div>
                <div class="mode-description">
                    Real-time transcription with speaker identification, conversation timeline, and AI-powered medical summaries
                </div>
            </div>
        </div>
    </div>
    
    <script>
        function selectMode(mode) {
            // Send mode selection to Streamlit
            window.parent.postMessage({
                type: 'streamlit:setComponentValue',
                value: mode
            }, '*');
        }
    </script>
    """, unsafe_allow_html=True)
    
    # Create columns for mode selection buttons
    col1, col2, col3 = st.columns([1, 2, 1])
    
    with col2:
        st.markdown("<br><br>", unsafe_allow_html=True)
        
        col_speech, col_voice = st.columns(2)
        
        with col_speech:
            if st.button("🎙️ Speech Recording Mode", key="speech_mode", use_container_width=True):
                st.session_state.selected_mode = 'speech'
                st.session_state.page = 'speech_recording'
                st.rerun()
        
        with col_voice:
            if st.button("📝 Voice-to-Text Mode", key="voice_mode", use_container_width=True):
                st.session_state.selected_mode = 'voice_to_text'
                st.session_state.page = 'voice_to_text'
                st.rerun()

def show_back_button():
    """Show back button to return to landing page"""
    if st.button("← Back to Home", key="back_button"):
        st.session_state.page = 'landing'
        st.session_state.selected_mode = None
        st.rerun()

# Main navigation logic
def main():
    if st.session_state.page == 'landing':
        show_landing_page()
    elif st.session_state.page == 'speech_recording':
        show_back_button()
        # Import here to avoid circular imports
        from speech_recording_page import show_speech_recording_page
        show_speech_recording_page()
    elif st.session_state.page == 'voice_to_text':
        show_back_button()
        # Import here to avoid circular imports
        from voice_to_text_page import show_voice_to_text_page
        show_voice_to_text_page()

if __name__ == "__main__":
    main()

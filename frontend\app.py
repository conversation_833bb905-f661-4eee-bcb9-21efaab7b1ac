import streamlit as st
import requests
import time
import json
from io import BytesIO

# Page configuration
st.set_page_config(
    page_title="Medical Voice Analyzer",
    page_icon="🩺",
    layout="wide",
    initial_sidebar_state="collapsed"
)

# Initialize session state for navigation
if 'page' not in st.session_state:
    st.session_state.page = 'landing'
if 'selected_mode' not in st.session_state:
    st.session_state.selected_mode = None

# Modern Medical UI CSS
st.markdown("""
<style>
    @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');

    /* Global Styles */
    .stApp {
        background: linear-gradient(135deg, #0a0e27 0%, #1a1d3a 50%, #2d3561 100%);
        color: #ffffff;
        font-family: 'Inter', sans-serif;
    }

    /* Hide Streamlit elements */
    #MainMenu {visibility: hidden;}
    footer {visibility: hidden;}
    header {visibility: hidden;}
    .stDeployButton {visibility: hidden;}

    /* Custom scrollbar */
    ::-webkit-scrollbar {
        width: 8px;
    }
    ::-webkit-scrollbar-track {
        background: rgba(26, 29, 58, 0.5);
    }
    ::-webkit-scrollbar-thumb {
        background: linear-gradient(45deg, #4f46e5, #7c3aed);
        border-radius: 4px;
    }

    /* Landing Page Container */
    .landing-container {
        max-width: 1400px;
        margin: 0 auto;
        padding: 2rem;
        min-height: 100vh;
        display: flex;
        flex-direction: column;
        justify-content: center;
    }

    /* Header Section */
    .header-section {
        text-align: center;
        margin-bottom: 4rem;
        padding: 4rem 2rem;
        background: linear-gradient(135deg, rgba(79, 70, 229, 0.1), rgba(124, 58, 237, 0.1));
        border-radius: 32px;
        border: 1px solid rgba(79, 70, 229, 0.2);
        backdrop-filter: blur(20px);
        position: relative;
        overflow: hidden;
    }

    .header-section::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(circle, rgba(79, 70, 229, 0.1) 0%, transparent 70%);
        animation: pulse 4s ease-in-out infinite;
    }

    @keyframes pulse {
        0%, 100% { transform: scale(1); opacity: 0.5; }
        50% { transform: scale(1.1); opacity: 0.8; }
    }

    .app-title {
        font-size: 4.5rem;
        font-weight: 800;
        background: linear-gradient(135deg, #4f46e5, #7c3aed, #ec4899);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        margin-bottom: 1.5rem;
        letter-spacing: -0.02em;
        position: relative;
        z-index: 1;
    }

    .app-subtitle {
        font-size: 1.4rem;
        color: #a5b4fc;
        margin-bottom: 2rem;
        max-width: 700px;
        margin-left: auto;
        margin-right: auto;
        line-height: 1.7;
        font-weight: 400;
        position: relative;
        z-index: 1;
    }

    .features-highlight {
        display: flex;
        justify-content: center;
        gap: 2rem;
        margin-top: 2rem;
        flex-wrap: wrap;
        position: relative;
        z-index: 1;
    }

    .feature-badge {
        background: rgba(79, 70, 229, 0.2);
        border: 1px solid rgba(79, 70, 229, 0.3);
        border-radius: 20px;
        padding: 0.5rem 1.5rem;
        font-size: 0.9rem;
        color: #c7d2fe;
        font-weight: 500;
    }

    /* Mode Selection Grid */
    .modes-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(450px, 1fr));
        gap: 3rem;
        margin: 3rem 0;
    }

    /* Mode Cards */
    .mode-card {
        background: linear-gradient(145deg, rgba(26, 29, 58, 0.8), rgba(10, 14, 39, 0.9));
        border-radius: 28px;
        padding: 3.5rem 2.5rem;
        text-align: center;
        border: 1px solid rgba(165, 180, 252, 0.1);
        transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
        backdrop-filter: blur(30px);
        position: relative;
        overflow: hidden;
        cursor: pointer;
    }

    .mode-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, rgba(79, 70, 229, 0.05), rgba(124, 58, 237, 0.05));
        opacity: 0;
        transition: opacity 0.5s ease;
        border-radius: 28px;
    }

    .mode-card:hover::before {
        opacity: 1;
    }

    .mode-card:hover {
        transform: translateY(-12px) scale(1.02);
        border-color: rgba(79, 70, 229, 0.4);
        box-shadow: 0 30px 60px rgba(79, 70, 229, 0.2);
    }

    .mode-icon {
        font-size: 5rem;
        margin-bottom: 2rem;
        display: block;
        filter: drop-shadow(0 8px 16px rgba(0,0,0,0.3));
        position: relative;
        z-index: 1;
    }

    .mode-title {
        font-size: 2.2rem;
        font-weight: 700;
        color: #ffffff;
        margin-bottom: 1.5rem;
        letter-spacing: -0.01em;
        position: relative;
        z-index: 1;
    }

    .mode-description {
        color: #a5b4fc;
        font-size: 1.2rem;
        line-height: 1.8;
        margin-bottom: 3rem;
        font-weight: 400;
        position: relative;
        z-index: 1;
    }
    
    /* Feature List */
    .features-list {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 1.5rem;
        margin-top: 2.5rem;
        position: relative;
        z-index: 1;
    }

    .feature-item {
        background: rgba(26, 29, 58, 0.4);
        border-radius: 16px;
        padding: 1.5rem;
        border: 1px solid rgba(165, 180, 252, 0.1);
        font-size: 1rem;
        color: #c7d2fe;
        text-align: left;
        transition: all 0.3s ease;
    }

    .feature-item:hover {
        background: rgba(79, 70, 229, 0.1);
        border-color: rgba(79, 70, 229, 0.3);
    }

    .feature-icon {
        color: #4f46e5;
        margin-right: 0.75rem;
        font-size: 1.1rem;
    }

    /* Custom Buttons */
    .mode-button-cta {
        background: linear-gradient(135deg, #4f46e5, #7c3aed);
        color: white;
        border: none;
        border-radius: 20px;
        padding: 1.2rem 3rem;
        font-size: 1.2rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        box-shadow: 0 10px 30px rgba(79, 70, 229, 0.3);
        font-family: 'Inter', sans-serif;
        letter-spacing: 0.01em;
        position: relative;
        z-index: 1;
        overflow: hidden;
    }

    .mode-button-cta::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
        transition: left 0.5s;
    }

    .mode-button-cta:hover::before {
        left: 100%;
    }

    .mode-button-cta:hover {
        background: linear-gradient(135deg, #5b52f0, #8b5cf6);
        transform: translateY(-3px);
        box-shadow: 0 15px 40px rgba(79, 70, 229, 0.4);
    }

    /* Back Button */
    .back-button {
        position: fixed;
        top: 2rem;
        left: 2rem;
        z-index: 1000;
        background: rgba(26, 29, 58, 0.9);
        color: #4f46e5;
        border: 1px solid rgba(79, 70, 229, 0.3);
        border-radius: 20px;
        padding: 1rem 2rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        backdrop-filter: blur(20px);
        font-family: 'Inter', sans-serif;
        font-size: 1rem;
    }

    .back-button:hover {
        background: rgba(79, 70, 229, 0.1);
        border-color: rgba(79, 70, 229, 0.5);
        transform: translateX(-3px);
        color: #6366f1;
    }

    /* Streamlit Button Styling */
    .stButton > button {
        background: linear-gradient(135deg, #4f46e5, #7c3aed) !important;
        color: white !important;
        border: none !important;
        border-radius: 20px !important;
        padding: 1.2rem 3rem !important;
        font-size: 1.2rem !important;
        font-weight: 600 !important;
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
        box-shadow: 0 10px 30px rgba(79, 70, 229, 0.3) !important;
        font-family: 'Inter', sans-serif !important;
        letter-spacing: 0.01em !important;
        width: 100% !important;
        height: 60px !important;
    }

    .stButton > button:hover {
        background: linear-gradient(135deg, #5b52f0, #8b5cf6) !important;
        transform: translateY(-3px) !important;
        box-shadow: 0 15px 40px rgba(79, 70, 229, 0.4) !important;
        border: none !important;
    }

    .stButton > button:active {
        transform: translateY(-1px) !important;
    }

    .stButton > button:focus {
        box-shadow: 0 15px 40px rgba(79, 70, 229, 0.4) !important;
        border: none !important;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .app-title {
            font-size: 3rem;
        }

        .modes-grid {
            grid-template-columns: 1fr;
            gap: 2rem;
        }

        .mode-card {
            padding: 2.5rem 2rem;
        }

        .mode-icon {
            font-size: 4rem;
        }

        .mode-title {
            font-size: 1.8rem;
        }
    }
</style>
""", unsafe_allow_html=True)

def show_landing_page():
    """Display the landing page with mode selection"""
    # Header section with inline styles
    st.markdown("""
    <div style="text-align: center; margin-bottom: 3rem; padding: 3rem 2rem;
                background: linear-gradient(135deg, rgba(79, 70, 229, 0.1), rgba(124, 58, 237, 0.1));
                border-radius: 24px; border: 1px solid rgba(79, 70, 229, 0.2);">
        <h1 style="font-size: 4rem; font-weight: 800; margin-bottom: 1rem;
                   background: linear-gradient(135deg, #4f46e5, #7c3aed, #ec4899);
                   -webkit-background-clip: text; -webkit-text-fill-color: transparent;">
            🩺 Medical Voice Analyzer
        </h1>
        <p style="font-size: 1.4rem; color: #a5b4fc; margin-bottom: 2rem; line-height: 1.7;">
            Advanced AI-powered medical conversation analysis system with dual-mode functionality
        </p>
    </div>
    """, unsafe_allow_html=True)

    # Mode selection using Streamlit columns and buttons
    col1, col2 = st.columns(2, gap="large")

    with col1:
        st.markdown("""
        <div style="background: linear-gradient(145deg, rgba(26, 29, 58, 0.8), rgba(10, 14, 39, 0.9));
                    border-radius: 28px; padding: 3rem 2rem; text-align: center;
                    border: 1px solid rgba(165, 180, 252, 0.1); height: 400px; margin-bottom: 2rem;">
            <div style="font-size: 5rem; margin-bottom: 2rem;">🎙️</div>
            <h3 style="font-size: 2rem; color: #ffffff; margin-bottom: 1.5rem; font-weight: 700;">Speech Recording Mode</h3>
            <p style="color: #a5b4fc; font-size: 1.1rem; line-height: 1.6; margin-bottom: 2rem;">
                Record high-quality medical conversations with professional audio capture.
                Save as MP3 files for future analysis.
            </p>
            <div style="color: #c7d2fe; font-size: 0.9rem;">
                ✓ Professional Quality • ✓ Secure Storage • ✓ Instant Processing
            </div>
        </div>
        """, unsafe_allow_html=True)

        if st.button("🎙️ Start Recording Mode", key="speech_mode", use_container_width=True, type="primary"):
            st.session_state.page = 'speech_recording'
            st.rerun()

    with col2:
        st.markdown("""
        <div style="background: linear-gradient(145deg, rgba(26, 29, 58, 0.8), rgba(10, 14, 39, 0.9));
                    border-radius: 28px; padding: 3rem 2rem; text-align: center;
                    border: 1px solid rgba(165, 180, 252, 0.1); height: 400px; margin-bottom: 2rem;">
            <div style="font-size: 5rem; margin-bottom: 2rem;">📝</div>
            <h3 style="font-size: 2rem; color: #ffffff; margin-bottom: 1.5rem; font-weight: 700;">Voice-to-Text Analysis</h3>
            <p style="color: #a5b4fc; font-size: 1.1rem; line-height: 1.6; margin-bottom: 2rem;">
                Real-time transcription with AI-powered speaker identification and
                comprehensive medical conversation analysis.
            </p>
            <div style="color: #c7d2fe; font-size: 0.9rem;">
                ✓ Real-time Processing • ✓ Speaker ID • ✓ AI Insights
            </div>
        </div>
        """, unsafe_allow_html=True)

        if st.button("📝 Start Analysis Mode", key="voice_mode", use_container_width=True, type="primary"):
            st.session_state.page = 'voice_to_text'
            st.rerun()
    
    with col2:
        st.markdown("<br><br>", unsafe_allow_html=True)
        
        col_speech, col_voice = st.columns(2)
        
        with col_speech:
            if st.button("🎙️ Speech Recording Mode", key="speech_mode", use_container_width=True):
                st.session_state.selected_mode = 'speech'
                st.session_state.page = 'speech_recording'
                st.rerun()
        
        with col_voice:
            if st.button("📝 Voice-to-Text Mode", key="voice_mode", use_container_width=True):
                st.session_state.selected_mode = 'voice_to_text'
                st.session_state.page = 'voice_to_text'
                st.rerun()

def show_back_button():
    """Show back button to return to landing page"""
    if st.button("← Back to Home", key="back_button"):
        st.session_state.page = 'landing'
        st.session_state.selected_mode = None
        st.rerun()

# Main navigation logic
def main():
    if st.session_state.page == 'landing':
        show_landing_page()
    elif st.session_state.page == 'speech_recording':
        show_back_button()
        # Import here to avoid circular imports
        from speech_recording_page import show_speech_recording_page
        show_speech_recording_page()
    elif st.session_state.page == 'voice_to_text':
        show_back_button()
        # Import here to avoid circular imports
        from voice_to_text_page import show_voice_to_text_page
        show_voice_to_text_page()

if __name__ == "__main__":
    main()

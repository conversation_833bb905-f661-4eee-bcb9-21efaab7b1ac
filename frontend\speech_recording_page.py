import streamlit as st
import streamlit.components.v1 as components
import requests
import time
from datetime import datetime

def show_speech_recording_page():
    """Display the speech recording mode page"""
    
    st.markdown("""
    <style>
        .speech-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 2rem;
        }
        
        .page-title {
            text-align: center;
            font-size: 2.5rem;
            color: #90caf9;
            margin-bottom: 1rem;
        }
        
        .page-subtitle {
            text-align: center;
            font-size: 1.2rem;
            color: #b0bec5;
            margin-bottom: 3rem;
        }
        
        .recording-panel {
            background: rgba(38, 50, 56, 0.6);
            border-radius: 20px;
            padding: 3rem;
            text-align: center;
            backdrop-filter: blur(12px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.3);
            margin-bottom: 2rem;
        }
        
        .recording-status {
            font-size: 1.5rem;
            margin-bottom: 2rem;
            padding: 1rem;
            border-radius: 10px;
            background: rgba(76, 175, 80, 0.1);
            border: 1px solid #4caf50;
            color: #81c784;
        }
        
        .recording-status.recording {
            background: rgba(244, 67, 54, 0.1);
            border-color: #f44336;
            color: #e57373;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }
        
        .control-buttons {
            display: flex;
            gap: 2rem;
            justify-content: center;
            margin: 2rem 0;
        }
        
        .record-button, .stop-button, .download-button {
            padding: 1rem 2rem;
            border-radius: 15px;
            border: none;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            min-width: 150px;
        }
        
        .record-button {
            background: linear-gradient(135deg, #4caf50 0%, #66bb6a 100%);
            color: white;
        }
        
        .record-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(76, 175, 80, 0.3);
        }
        
        .stop-button {
            background: linear-gradient(135deg, #f44336 0%, #ef5350 100%);
            color: white;
        }
        
        .stop-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(244, 67, 54, 0.3);
        }
        
        .download-button {
            background: linear-gradient(135deg, #2196f3 0%, #42a5f5 100%);
            color: white;
        }
        
        .download-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(33, 150, 243, 0.3);
        }
        
        .recording-info {
            background: rgba(55, 71, 79, 0.4);
            border-radius: 15px;
            padding: 2rem;
            margin-top: 2rem;
        }
        
        .info-title {
            font-size: 1.3rem;
            color: #90caf9;
            margin-bottom: 1rem;
        }
        
        .info-list {
            color: #b0bec5;
            line-height: 1.8;
        }
    </style>
    """, unsafe_allow_html=True)
    
    st.markdown("""
    <div class="speech-container">
        <h1 class="page-title">🎙️ Speech Recording Mode</h1>
        <p class="page-subtitle">Record high-quality medical conversations for future analysis</p>
    </div>
    """, unsafe_allow_html=True)
    
    # Recording interface using HTML/JavaScript
    components.html("""
    <div class="recording-panel">
        <div id="recordingStatus" class="recording-status">Ready to Record</div>
        
        <div class="control-buttons">
            <button id="startBtn" class="record-button" onclick="startRecording()">
                🎙️ Start Recording
            </button>
            <button id="stopBtn" class="stop-button" onclick="stopRecording()" disabled>
                ⏹️ Stop Recording
            </button>
            <button id="downloadBtn" class="download-button" onclick="downloadRecording()" disabled>
                💾 Download MP3
            </button>
        </div>
        
        <div id="recordingTime" style="font-size: 1.2rem; color: #90caf9; margin-top: 1rem;">
            Duration: 00:00
        </div>
    </div>
    
    <div class="recording-info">
        <div class="info-title">📋 Recording Features</div>
        <div class="info-list">
            • High-quality audio recording (44.1kHz, 16-bit)<br>
            • Automatic MP3 compression for easy storage<br>
            • Real-time duration tracking<br>
            • Instant download after recording<br>
            • Perfect for medical consultations and meetings<br>
            • Compatible with all major audio players
        </div>
    </div>
    
    <script>
        let mediaRecorder;
        let audioChunks = [];
        let startTime;
        let timerInterval;
        
        async function startRecording() {
            try {
                const stream = await navigator.mediaDevices.getUserMedia({ 
                    audio: {
                        sampleRate: 44100,
                        channelCount: 2,
                        volume: 1.0
                    } 
                });
                
                mediaRecorder = new MediaRecorder(stream, {
                    mimeType: 'audio/webm;codecs=opus'
                });
                
                audioChunks = [];
                
                mediaRecorder.ondataavailable = event => {
                    audioChunks.push(event.data);
                };
                
                mediaRecorder.onstop = () => {
                    const audioBlob = new Blob(audioChunks, { type: 'audio/webm' });
                    const audioUrl = URL.createObjectURL(audioBlob);
                    
                    // Enable download button
                    document.getElementById('downloadBtn').disabled = false;
                    document.getElementById('downloadBtn').onclick = () => downloadAudio(audioBlob);
                    
                    document.getElementById('recordingStatus').textContent = 'Recording Complete - Ready to Download';
                    document.getElementById('recordingStatus').className = 'recording-status';
                };
                
                mediaRecorder.start();
                startTime = Date.now();
                
                // Update UI
                document.getElementById('startBtn').disabled = true;
                document.getElementById('stopBtn').disabled = false;
                document.getElementById('recordingStatus').textContent = '🔴 Recording in Progress...';
                document.getElementById('recordingStatus').className = 'recording-status recording';
                
                // Start timer
                timerInterval = setInterval(updateTimer, 1000);
                
            } catch (error) {
                console.error('Error accessing microphone:', error);
                document.getElementById('recordingStatus').textContent = 'Error: Could not access microphone';
            }
        }
        
        function stopRecording() {
            if (mediaRecorder && mediaRecorder.state === 'recording') {
                mediaRecorder.stop();
                mediaRecorder.stream.getTracks().forEach(track => track.stop());
                
                // Update UI
                document.getElementById('startBtn').disabled = false;
                document.getElementById('stopBtn').disabled = true;
                
                // Stop timer
                clearInterval(timerInterval);
            }
        }
        
        function updateTimer() {
            const elapsed = Date.now() - startTime;
            const minutes = Math.floor(elapsed / 60000);
            const seconds = Math.floor((elapsed % 60000) / 1000);
            document.getElementById('recordingTime').textContent = 
                `Duration: ${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        }
        
        async function downloadAudio(audioBlob) {
            try {
                // Save to backend first
                const formData = new FormData();
                formData.append('audio', audioBlob, 'recording.webm');

                document.getElementById('recordingStatus').textContent = 'Saving recording...';

                const response = await fetch('http://localhost:8000/speech-recording/save', {
                    method: 'POST',
                    body: formData
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const result = await response.json();

                if (result.success) {
                    // Download the MP3 file
                    const downloadUrl = `http://localhost:8000/speech-recording/download/${result.filename}`;
                    const downloadLink = document.createElement('a');
                    downloadLink.href = downloadUrl;
                    downloadLink.download = result.filename;
                    downloadLink.click();

                    document.getElementById('recordingStatus').textContent =
                        `Recording saved as ${result.filename} (${Math.round(result.duration)}s)`;
                } else {
                    throw new Error(result.message || 'Failed to save recording');
                }

            } catch (error) {
                console.error('Error saving recording:', error);
                document.getElementById('recordingStatus').textContent = 'Error saving recording';

                // Fallback to local download
                const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
                const filename = `medical-recording-${timestamp}.webm`;

                const downloadLink = document.createElement('a');
                downloadLink.href = URL.createObjectURL(audioBlob);
                downloadLink.download = filename;
                downloadLink.click();
            }

            // Reset for new recording
            setTimeout(() => {
                document.getElementById('downloadBtn').disabled = true;
                document.getElementById('recordingStatus').textContent = 'Ready to Record';
                document.getElementById('recordingTime').textContent = 'Duration: 00:00';
            }, 3000);
        }
    </script>
    """, height=600)
    
    # Additional information
    st.markdown("""
    <div style="margin-top: 2rem; padding: 1.5rem; background: rgba(55, 71, 79, 0.3); border-radius: 15px;">
        <h3 style="color: #90caf9; margin-bottom: 1rem;">💡 Tips for Best Recording Quality</h3>
        <ul style="color: #b0bec5; line-height: 1.8;">
            <li>Ensure you're in a quiet environment with minimal background noise</li>
            <li>Position the microphone 6-12 inches from the speakers</li>
            <li>Speak clearly and at a moderate pace</li>
            <li>Test your microphone before important recordings</li>
            <li>Keep the device plugged in for longer recordings</li>
        </ul>
    </div>
    """, unsafe_allow_html=True)

import streamlit as st
import streamlit.components.v1 as components
import requests
import time
from datetime import datetime

def show_speech_recording_page():
    """Display the speech recording mode page"""
    
    st.markdown("""
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');

        .speech-container {
            max-width: 900px;
            margin: 0 auto;
            padding: 2rem;
            font-family: 'Inter', sans-serif;
        }

        .page-header {
            text-align: center;
            margin-bottom: 3rem;
            padding: 3rem 2rem;
            background: linear-gradient(135deg, rgba(79, 70, 229, 0.1), rgba(124, 58, 237, 0.1));
            border-radius: 24px;
            border: 1px solid rgba(79, 70, 229, 0.2);
            backdrop-filter: blur(20px);
        }

        .page-title {
            font-size: 3rem;
            font-weight: 800;
            background: linear-gradient(135deg, #4f46e5, #7c3aed);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 1rem;
            letter-spacing: -0.02em;
        }

        .page-subtitle {
            font-size: 1.3rem;
            color: #a5b4fc;
            margin-bottom: 1.5rem;
            font-weight: 400;
            line-height: 1.6;
        }

        .feature-badges {
            display: flex;
            justify-content: center;
            gap: 1rem;
            flex-wrap: wrap;
            margin-top: 1.5rem;
        }

        .feature-badge {
            background: rgba(79, 70, 229, 0.2);
            border: 1px solid rgba(79, 70, 229, 0.3);
            border-radius: 20px;
            padding: 0.5rem 1.5rem;
            font-size: 0.9rem;
            color: #c7d2fe;
            font-weight: 500;
        }

        .recording-panel {
            background: linear-gradient(145deg, rgba(26, 29, 58, 0.8), rgba(10, 14, 39, 0.9));
            border-radius: 28px;
            padding: 4rem 3rem;
            text-align: center;
            border: 1px solid rgba(165, 180, 252, 0.1);
            backdrop-filter: blur(30px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            margin-bottom: 2rem;
            position: relative;
            overflow: hidden;
        }

        .recording-panel::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(79, 70, 229, 0.05), rgba(124, 58, 237, 0.05));
            border-radius: 28px;
        }

        .recording-status {
            font-size: 1.6rem;
            margin-bottom: 2.5rem;
            padding: 1.5rem;
            border-radius: 16px;
            background: rgba(79, 70, 229, 0.1);
            border: 1px solid rgba(79, 70, 229, 0.3);
            color: #a5b4fc;
            font-weight: 600;
            position: relative;
            z-index: 1;
        }
        
        .recording-status.recording {
            background: rgba(236, 72, 153, 0.1);
            border-color: #ec4899;
            color: #f9a8d4;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; transform: scale(1); }
            50% { opacity: 0.8; transform: scale(1.02); }
            100% { opacity: 1; transform: scale(1); }
        }

        .control-buttons {
            display: flex;
            gap: 2rem;
            justify-content: center;
            margin: 3rem 0;
            position: relative;
            z-index: 1;
        }

        .record-button, .stop-button, .download-button {
            padding: 1.2rem 3rem;
            border-radius: 20px;
            border: none;
            font-size: 1.2rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            min-width: 180px;
            font-family: 'Inter', sans-serif;
            position: relative;
            overflow: hidden;
        }

        .record-button {
            background: linear-gradient(135deg, #10b981, #059669);
            color: white;
            box-shadow: 0 10px 30px rgba(16, 185, 129, 0.3);
        }

        .record-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .record-button:hover::before {
            left: 100%;
        }

        .record-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 40px rgba(16, 185, 129, 0.4);
            background: linear-gradient(135deg, #059669, #047857);
        }

        .stop-button {
            background: linear-gradient(135deg, #ef4444, #dc2626);
            color: white;
            box-shadow: 0 10px 30px rgba(239, 68, 68, 0.3);
        }

        .stop-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 40px rgba(239, 68, 68, 0.4);
            background: linear-gradient(135deg, #dc2626, #b91c1c);
        }

        .download-button {
            background: linear-gradient(135deg, #3b82f6, #2563eb);
            color: white;
            box-shadow: 0 10px 30px rgba(59, 130, 246, 0.3);
        }

        .download-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 40px rgba(59, 130, 246, 0.4);
            background: linear-gradient(135deg, #2563eb, #1d4ed8);
        }

        .recording-info {
            background: linear-gradient(145deg, rgba(26, 29, 58, 0.6), rgba(10, 14, 39, 0.7));
            border-radius: 20px;
            padding: 2.5rem;
            margin-top: 2rem;
            border: 1px solid rgba(165, 180, 252, 0.1);
            backdrop-filter: blur(20px);
        }

        .info-title {
            font-size: 1.4rem;
            color: #a5b4fc;
            margin-bottom: 1.5rem;
            font-weight: 600;
        }

        .info-list {
            color: #c7d2fe;
            line-height: 2;
            font-size: 1rem;
        }

        .info-item {
            margin-bottom: 0.8rem;
            padding-left: 1.5rem;
            position: relative;
        }

        .info-item::before {
            content: '✓';
            position: absolute;
            left: 0;
            color: #4f46e5;
            font-weight: bold;
        }

        .duration-display {
            font-size: 2rem;
            font-weight: 700;
            color: #4f46e5;
            margin: 1.5rem 0;
            font-family: 'Courier New', monospace;
            text-shadow: 0 0 10px rgba(79, 70, 229, 0.3);
        }

        @media (max-width: 768px) {
            .control-buttons {
                flex-direction: column;
                align-items: center;
                gap: 1.5rem;
            }

            .record-button, .stop-button, .download-button {
                width: 100%;
                max-width: 280px;
            }
        }
    </style>
    """, unsafe_allow_html=True)

    st.markdown("""
    <div class="speech-container">
        <div class="page-header">
            <h1 class="page-title">🎙️ Speech Recording Mode</h1>
            <p class="page-subtitle">Record high-quality medical conversations with professional audio capture</p>
            <div class="feature-badges">
                <div class="feature-badge">📊 Professional Quality</div>
                <div class="feature-badge">🔒 Secure Storage</div>
                <div class="feature-badge">⚡ Instant Processing</div>
            </div>
        </div>
    </div>
    """, unsafe_allow_html=True)
    
    # Modern recording interface using HTML/JavaScript
    components.html("""
    <div class="recording-panel">
        <div id="recordingStatus" class="recording-status">🎙️ Ready to Record</div>
        <div id="recordingTime" class="duration-display">Duration: 00:00</div>

        <div class="control-buttons">
            <button id="startBtn" class="record-button" onclick="startRecording()">
                🎙️ Start Recording
            </button>
            <button id="stopBtn" class="stop-button" onclick="stopRecording()" disabled>
                ⏹️ Stop Recording
            </button>
            <button id="downloadBtn" class="download-button" onclick="downloadRecording()" disabled>
                💾 Download MP3
            </button>
        </div>
        
        <div id="recordingTime" style="font-size: 1.2rem; color: #90caf9; margin-top: 1rem;">
            Duration: 00:00
        </div>
    </div>
    
    <div class="recording-info">
        <div class="info-title">📋 Recording Features</div>
        <div class="info-list">
            • High-quality audio recording (44.1kHz, 16-bit)<br>
            • Automatic MP3 compression for easy storage<br>
            • Real-time duration tracking<br>
            • Instant download after recording<br>
            • Perfect for medical consultations and meetings<br>
            • Compatible with all major audio players
        </div>
    </div>
    
    <script>
        let mediaRecorder;
        let audioChunks = [];
        let startTime;
        let timerInterval;
        
        async function startRecording() {
            try {
                const stream = await navigator.mediaDevices.getUserMedia({ 
                    audio: {
                        sampleRate: 44100,
                        channelCount: 2,
                        volume: 1.0
                    } 
                });
                
                mediaRecorder = new MediaRecorder(stream, {
                    mimeType: 'audio/webm;codecs=opus'
                });
                
                audioChunks = [];
                
                mediaRecorder.ondataavailable = event => {
                    audioChunks.push(event.data);
                };
                
                mediaRecorder.onstop = () => {
                    const audioBlob = new Blob(audioChunks, { type: 'audio/webm' });
                    const audioUrl = URL.createObjectURL(audioBlob);
                    
                    // Enable download button
                    document.getElementById('downloadBtn').disabled = false;
                    document.getElementById('downloadBtn').onclick = () => downloadAudio(audioBlob);
                    
                    document.getElementById('recordingStatus').textContent = 'Recording Complete - Ready to Download';
                    document.getElementById('recordingStatus').className = 'recording-status';
                };
                
                mediaRecorder.start();
                startTime = Date.now();
                
                // Update UI
                document.getElementById('startBtn').disabled = true;
                document.getElementById('stopBtn').disabled = false;
                document.getElementById('recordingStatus').textContent = '🔴 Recording in Progress...';
                document.getElementById('recordingStatus').className = 'recording-status recording';
                
                // Start timer
                timerInterval = setInterval(updateTimer, 1000);
                
            } catch (error) {
                console.error('Error accessing microphone:', error);
                document.getElementById('recordingStatus').textContent = 'Error: Could not access microphone';
            }
        }
        
        function stopRecording() {
            if (mediaRecorder && mediaRecorder.state === 'recording') {
                mediaRecorder.stop();
                mediaRecorder.stream.getTracks().forEach(track => track.stop());
                
                // Update UI
                document.getElementById('startBtn').disabled = false;
                document.getElementById('stopBtn').disabled = true;
                
                // Stop timer
                clearInterval(timerInterval);
            }
        }
        
        function updateTimer() {
            const elapsed = Date.now() - startTime;
            const minutes = Math.floor(elapsed / 60000);
            const seconds = Math.floor((elapsed % 60000) / 1000);
            document.getElementById('recordingTime').textContent = 
                `Duration: ${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        }
        
        async function downloadAudio(audioBlob) {
            try {
                // Save to backend first
                const formData = new FormData();
                formData.append('audio', audioBlob, 'recording.webm');

                document.getElementById('recordingStatus').textContent = 'Saving recording...';

                const response = await fetch('http://localhost:8000/speech-recording/save', {
                    method: 'POST',
                    body: formData
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const result = await response.json();

                if (result.success) {
                    // Download the MP3 file
                    const downloadUrl = `http://localhost:8000/speech-recording/download/${result.filename}`;
                    const downloadLink = document.createElement('a');
                    downloadLink.href = downloadUrl;
                    downloadLink.download = result.filename;
                    downloadLink.click();

                    document.getElementById('recordingStatus').textContent =
                        `Recording saved as ${result.filename} (${Math.round(result.duration)}s)`;
                } else {
                    throw new Error(result.message || 'Failed to save recording');
                }

            } catch (error) {
                console.error('Error saving recording:', error);
                document.getElementById('recordingStatus').textContent = 'Error saving recording';

                // Fallback to local download
                const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
                const filename = `medical-recording-${timestamp}.webm`;

                const downloadLink = document.createElement('a');
                downloadLink.href = URL.createObjectURL(audioBlob);
                downloadLink.download = filename;
                downloadLink.click();
            }

            // Reset for new recording
            setTimeout(() => {
                document.getElementById('downloadBtn').disabled = true;
                document.getElementById('recordingStatus').textContent = 'Ready to Record';
                document.getElementById('recordingTime').textContent = 'Duration: 00:00';
            }, 3000);
        }
    </script>
    """, height=600)
    
    # Modern information section
    st.markdown("""
    <div class="recording-info">
        <h3 class="info-title">💡 Professional Recording Guidelines</h3>
        <div class="info-list">
            <div class="info-item">Ensure quiet environment with minimal background noise</div>
            <div class="info-item">Position microphone 6-12 inches from speakers</div>
            <div class="info-item">Speak clearly and maintain moderate pace</div>
            <div class="info-item">Test microphone settings before important recordings</div>
            <div class="info-item">Keep device charged for extended recording sessions</div>
            <div class="info-item">Use headphones to monitor audio quality in real-time</div>
        </div>
    </div>
    """, unsafe_allow_html=True)

import streamlit as st
import streamlit.components.v1 as components

st.set_page_config(page_title="Medical Voice Analyzer", layout="wide")

st.markdown("""
    <h1 style='text-align: center; color: #1976D2;'> Doctor-Patient Voice Conversation Analyzer</h1>
    <p style='text-align: center; font-size: 18px;'>Record medical conversations, separate speakers, and get AI-based summaries</p>
""", unsafe_allow_html=True)

components.html("""
<html>
<head>
<style>
    * { box-sizing: border-box; margin: 0; padding: 0; }

    body {
        font-family: 'Segoe UI', sans-serif;
        background: linear-gradient(to right, #e3f2fd, #ffffff);
        padding: 30px;
    }

    .container {
        max-width: 100%;
        margin: auto;
        display: flex;
        flex-direction: row;
        gap: 30px;
        align-items: flex-start;
    }

    .left-panel {
        flex: 0.3;
        padding: 20px;
        border-radius: 20px;
        background: #ffffff;
        box-shadow: 0 4px 12px rgba(0,0,0,0.08);
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: flex-start;
    }

    .right-panel {
        flex: 0.7;
        max-height: 90vh;
        overflow-y: auto;
        padding: 30px;
        border-radius: 20px;
        background: #ffffff;
        box-shadow: 0 4px 12px rgba(0,0,0,0.08);
    }

    h2 {
        color: #1976D2;
        margin-bottom: 10px;
    }

    .button-row {
        display: flex;
        gap: 15px;
        margin-top: 20px;
    }

    .btn {
        padding: 12px 25px;
        font-size: 15px;
        border: none;
        border-radius: 30px;
        cursor: pointer;
        font-weight: bold;
        transition: 0.2s ease;
        width: 180px;
        text-align: center;
    }

    .btn-start {
        background-color: #43a047;
        color: white;
    }

    .btn-stop {
        background-color: #e53935;
        color: white;
    }

    .btn-summary {
        margin-top: 20px;
        background: #1976D2;
        color: white;
    }

    .btn:hover {
        opacity: 0.9;
    }

    .status-indicator {
        margin-top: 15px;
        padding: 10px 20px;
        background-color: #e3f2fd;
        border-radius: 20px;
        font-weight: 500;
        color: #1976d2;
        font-size: 14px;
    }

    .live-section {
        margin-bottom: 25px;
        background: #f1f8e9;
        border-left: 5px solid #4CAF50;
        border-radius: 10px;
        padding: 15px 20px;
    }

    .timeline-item {
        margin-top: 15px;
        padding: 15px;
        border-left: 5px solid #1976D2;
        background: #fafafa;
        border-radius: 10px;
    }

    .speaker-doctor { border-left-color: #388e3c; }
    .speaker-patient { border-left-color: #1976D2; }

    .transcript-content, .summary-result {
        background: #f4f6f8;
        border-radius: 10px;
        padding: 15px 20px;
        margin-top: 10px;
        color: #444;
    }

    .section-title {
        font-weight: bold;
        font-size: 18px;
        margin-top: 25px;
        color: #333;
    }

    @media (max-width: 1024px) {
        .container {
            flex-direction: column;
        }
        .left-panel, .right-panel {
            flex: 1;
            width: 100%;
        }
        .button-row {
            justify-content: center;
            flex-wrap: wrap;
        }
    }
</style>
</head>
<body>
    <div class="container">
        <div class="left-panel">
            <h2> Start Recording</h2>
            <div class="button-row">
                <button class="btn btn-start" id="startBtn">Start</button>
                <button class="btn btn-stop" id="stopBtn" disabled>Stop</button>
            </div>
            <div class="status-indicator" id="status">Waiting to start...</div>
        </div>
        <div class="right-panel">
            <div class="live-section">
                <div><strong>Live Words:</strong> <span id="liveWords">Listening...</span></div>
            </div>
            <div id="result"></div>
        </div>
    </div>

    <script>
        let mediaRecorder, audioChunks = [], recognition;
        const startBtn = document.getElementById('startBtn');
        const stopBtn = document.getElementById('stopBtn');
        const statusDiv = document.getElementById('status');
        const liveWordsDiv = document.getElementById('liveWords');
        const resultDiv = document.getElementById('result');

        function initLiveText() {
            const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
            if (!SpeechRecognition) return;
            recognition = new SpeechRecognition();
            recognition.continuous = true;
            recognition.interimResults = true;
            recognition.lang = 'en-US';

            recognition.onresult = e => {
                let text = '';
                for (let i = e.resultIndex; i < e.results.length; i++) {
                    text += e.results[i][0].transcript;
                }
                liveWordsDiv.innerText = text.split(' ').slice(-8).join(' ') || 'Listening...';
            };
        }

        startBtn.onclick = async () => {
            try {
                audioChunks = [];
                const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
                mediaRecorder = new MediaRecorder(stream);
                mediaRecorder.start();
                initLiveText();
                recognition?.start();

                startBtn.disabled = true;
                stopBtn.disabled = false;
                statusDiv.innerText = ' Recording...';

                mediaRecorder.ondataavailable = e => audioChunks.push(e.data);

                mediaRecorder.onstop = async () => {
                    recognition?.stop();
                    statusDiv.innerText = ' Processing...';
                    const blob = new Blob(audioChunks, { type: 'audio/wav' });
                    const formData = new FormData();
                    formData.append('file', blob, 'audio.wav');

                    const res = await fetch('http://localhost:8000/analyze-conversation/', {
                        method: 'POST', body: formData
                    });
                    const data = await res.json();
                    localStorage.setItem("doctor_patient_analysis", JSON.stringify(data));

                    let html = ``;

                    if (data.full_conversation) {
                        html += `<div class="section-title"> Conversation Timeline</div>`;
                        data.full_conversation.forEach(seg => {
                            const cls = seg.speaker === 'doctor' ? 'speaker-doctor' : 'speaker-patient';
                            html += `<div class="timeline-item ${cls}">
                                <strong>${seg.speaker}</strong>: ${seg.text}
                            </div>`;
                        });
                    }

                    html += `<button class="btn btn-summary" onclick="generateSummary()"> Generate Summary</button>
                             <div id="summaryResult"></div>`;
                    resultDiv.innerHTML = html;

                    startBtn.disabled = false;
                    stopBtn.disabled = true;
                    statusDiv.innerText = ' Done';
                };
            } catch (err) {
                console.error(err);
                statusDiv.innerText = ' Error accessing mic';
            }
        };

        stopBtn.onclick = () => {
            if (mediaRecorder?.state === 'recording') mediaRecorder.stop();
        };

        async function generateSummary() {
            const summaryBtn = document.querySelector('.btn-summary');
            summaryBtn.disabled = true;
            summaryBtn.innerText = ' Summarizing...';

            const data = JSON.parse(localStorage.getItem('doctor_patient_analysis'));
            const res = await fetch('http://localhost:8000/summarize-conversation/', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    transcript: data.transcript,
                    timeline: data.full_conversation
                })
            });
            const result = await res.json();
            const resultBox = document.getElementById("summaryResult");
            resultBox.innerHTML = `<div class="summary-result">${result.summary || ' Error generating summary'}</div>`;
            summaryBtn.innerText = 'Generate Summary';
            summaryBtn.disabled = false;
        }
    </script>
</body>
</html>
""", height=880, scrolling=True)

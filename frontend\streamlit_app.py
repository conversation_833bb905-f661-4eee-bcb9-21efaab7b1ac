import streamlit as st
import streamlit.components.v1 as components

st.set_page_config(page_title="Medical Voice Analyzer", layout="wide")

st.markdown("""
    <h1 style='text-align: center; color: #90caf9;'> Doctor-Patient Voice Conversation Analyzer</h1>
    <p style='text-align: center; font-size: 18px; color: #b0bec5;'>Record medical conversations, separate speakers, and get AI-based summaries</p>
""", unsafe_allow_html=True)

components.html("""
<html>
<head>
<style>
    * { margin: 0; padding: 0; box-sizing: border-box; }

    body {
        font-family: 'Segoe UI', sans-serif;
        background: #0f0f0f;
        background-image: radial-gradient(#212121 1px, transparent 1px),
                          radial-gradient(#212121 1px, transparent 1px);
        background-size: 40px 40px;
        background-position: 0 0, 20px 20px;
        animation: moveBackground 60s linear infinite;
        padding: 30px;
        color: #e0e0e0;
    }

    @keyframes moveBackground {
        from { background-position: 0 0, 20px 20px; }
        to { background-position: 1000px 1000px, 1020px 1020px; }
    }

    .container {
        max-width: 100%;
        margin: auto;
        display: flex;
        flex-direction: row;
        gap: 30px;
        align-items: flex-start;
    }

    .left-panel, .right-panel {
        border-radius: 20px;
        backdrop-filter: blur(12px);
        background: rgba(38, 50, 56, 0.6);
        box-shadow: 0 8px 25px rgba(0,0,0,0.5);
        padding: 30px;
    }

    .left-panel {
        flex: 0.3;
        display: flex;
        flex-direction: column;
        align-items: center;
    }

    .right-panel {
        flex: 0.7;
        max-height: 90vh;
        overflow-y: auto;
    }

    .neon-title {
        text-align: center;
        font-size: 36px;
        font-weight: 800;
        margin-bottom: 10px;
        background: linear-gradient(90deg, #00e5ff, #7c4dff, #00e5ff);
        background-size: 300%;
        color: transparent;
        -webkit-background-clip: text;
        background-clip: text;
        animation: glowMove 5s infinite linear;
        text-shadow: 0 0 10px #00e5ff99, 0 0 20px #7c4dff55;
    }

    @keyframes glowMove {
        0% { background-position: 0%; }
        100% { background-position: 300%; }
    }

    .subtext {
        text-align: center;
        font-size: 18px;
        color: #b0bec5;
        margin-bottom: 30px;
    }

    /* Same previous panel styles (reuse from dark version) */
    .button-row {
        display: flex;
        gap: 15px;
        margin-top: 20px;
    }

    .btn {
        padding: 12px 25px;
        font-size: 15px;
        border: none;
        border-radius: 30px;
        cursor: pointer;
        font-weight: bold;
        width: 180px;
        text-align: center;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(0,0,0,0.3);
    }

    .btn-start { background-color: #00e676; color: #000; }
    .btn-stop { background-color: #ff1744; color: white; }
    .btn-summary { margin-top: 20px; background: #2979ff; color: white; }

    .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(0,0,0,0.5);
        opacity: 0.95;
    }

    .status-indicator {
        margin-top: 20px;
        padding: 12px 20px;
        background: rgba(25, 118, 210, 0.15);
        border: 1px solid #90caf9;
        border-radius: 30px;
        font-weight: 500;
        font-size: 14px;
        color: #90caf9;
    }

    .live-section {
        margin-bottom: 30px;
        background: rgba(0, 230, 118, 0.1);
        border-left: 5px solid #00e676;
        border-radius: 10px;
        padding: 15px 20px;
        color: #c8e6c9;
        animation: pulse 2s infinite ease-in-out;
    }

    @keyframes pulse {
        0% { box-shadow: 0 0 0px #00e67633; }
        50% { box-shadow: 0 0 12px #00e67699; }
        100% { box-shadow: 0 0 0px #00e67633; }
    }

    .timeline-item {
        margin-top: 15px;
        padding: 15px;
        border-left: 5px solid #90caf9;
        background: rgba(55,71,79,0.6);
        border-radius: 10px;
        color: #ffffff;
        transition: all 0.3s;
    }

    .timeline-item:hover {
        transform: scale(1.01);
        background: rgba(69, 90, 100, 0.8);
    }

    .speaker-doctor { border-left-color: #00e676; }
    .speaker-patient { border-left-color: #2979ff; }

    .section-title {
        font-weight: bold;
        font-size: 20px;
        margin-top: 30px;
        color: #ffffff;
    }

    .transcript-content, .summary-result {
        background: rgba(55,71,79,0.7);
        border-radius: 10px;
        padding: 15px 20px;
        margin-top: 10px;
        color: #fff;
        box-shadow: inset 0 0 10px rgba(0,0,0,0.4);
    }

    @media (max-width: 1024px) {
        .container {
            flex-direction: column;
        }
        .left-panel, .right-panel {
            flex: 1;
            width: 100%;
        }
        .button-row {
            justify-content: center;
            flex-wrap: wrap;
        }
    }
</style>
</head>
<body>
    <div class="neon-title">Doctor-Patient Voice Conversation Analyzer</div>
    <div class="subtext">Record medical conversations, separate speakers, and get AI-based summaries</div>

    <div class="container">
        <div class="left-panel">
            <h2>🎙️ Start Recording</h2>
            <div class="button-row">
                <button class="btn btn-start" id="startBtn">Start</button>
                <button class="btn btn-stop" id="stopBtn" disabled>Stop</button>
            </div>
            <div class="status-indicator" id="status">Waiting to start...</div>
        </div>
        <div class="right-panel">
            <div class="live-section">
                <div><strong>Live Words:</strong> <span id="liveWords">Listening...</span></div>
            </div>
            <div id="result"></div>
        </div>
    </div>
<script>
    // Keep all previous JavaScript logic here (no change needed)
</script>
</body>
</html>

""", height=900, scrolling=True)

import streamlit as st
import streamlit.components.v1 as components

def show_voice_to_text_page():
    """Display the voice-to-text analysis mode page"""
    
    st.markdown("""
    <style>
        .voice-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }
        
        .page-title {
            text-align: center;
            font-size: 2.5rem;
            color: #90caf9;
            margin-bottom: 1rem;
        }
        
        .page-subtitle {
            text-align: center;
            font-size: 1.2rem;
            color: #b0bec5;
            margin-bottom: 3rem;
        }
    </style>
    """, unsafe_allow_html=True)
    
    st.markdown("""
    <div class="voice-container">
        <h1 class="page-title">📝 Voice-to-Text Analysis</h1>
        <p class="page-subtitle">Real-time transcription with AI-powered speaker identification and medical conversation analysis</p>
    </div>
    """, unsafe_allow_html=True)
    
    # Your existing voice-to-text functionality
    components.html("""
<html>
<head>
<style>
    * { margin: 0; padding: 0; box-sizing: border-box; }

    body {
        font-family: 'Segoe UI', sans-serif;
        background: #0f0f0f;
        background-image: radial-gradient(#212121 1px, transparent 1px),
                          radial-gradient(#212121 1px, transparent 1px);
        background-size: 40px 40px;
        background-position: 0 0, 20px 20px;
        animation: moveBackground 60s linear infinite;
        padding: 30px;
        color: #e0e0e0;
    }

    @keyframes moveBackground {
        from { background-position: 0 0, 20px 20px; }
        to { background-position: 1000px 1000px, 1020px 1020px; }
    }

    .container {
        max-width: 100%;
        margin: auto;
        display: flex;
        flex-direction: row;
        gap: 30px;
        align-items: flex-start;
    }

    .left-panel, .right-panel {
        border-radius: 20px;
        backdrop-filter: blur(12px);
        background: rgba(38, 50, 56, 0.6);
        box-shadow: 0 8px 25px rgba(0,0,0,0.5);
        padding: 30px;
    }

    .left-panel {
        flex: 0 0 400px;
        position: sticky;
        top: 30px;
    }

    .right-panel {
        flex: 1;
        min-height: 600px;
    }

    .section-title {
        font-size: 1.4rem;
        font-weight: 600;
        color: #90caf9;
        margin-bottom: 20px;
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .recording-controls {
        display: flex;
        flex-direction: column;
        gap: 20px;
        margin-bottom: 30px;
    }

    .button-group {
        display: flex;
        gap: 15px;
    }

    .btn {
        padding: 15px 25px;
        border: none;
        border-radius: 12px;
        font-size: 1rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        flex: 1;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .btn-start {
        background: linear-gradient(135deg, #4caf50 0%, #66bb6a 100%);
        color: white;
        box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
    }

    .btn-start:hover:not(:disabled) {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(76, 175, 80, 0.4);
    }

    .btn-stop {
        background: linear-gradient(135deg, #f44336 0%, #ef5350 100%);
        color: white;
        box-shadow: 0 4px 15px rgba(244, 67, 54, 0.3);
    }

    .btn-stop:hover:not(:disabled) {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(244, 67, 54, 0.4);
    }

    .btn:disabled {
        opacity: 0.5;
        cursor: not-allowed;
        transform: none !important;
    }

    .status-indicator {
        padding: 15px;
        border-radius: 12px;
        text-align: center;
        font-weight: 600;
        margin-bottom: 20px;
        transition: all 0.3s ease;
    }

    .status-ready {
        background: rgba(76, 175, 80, 0.1);
        border: 2px solid #4caf50;
        color: #81c784;
    }

    .status-recording {
        background: rgba(244, 67, 54, 0.1);
        border: 2px solid #f44336;
        color: #e57373;
        animation: pulse 2s infinite;
    }

    .status-processing {
        background: rgba(255, 193, 7, 0.1);
        border: 2px solid #ffc107;
        color: #ffcc02;
    }

    .status-done {
        background: rgba(33, 150, 243, 0.1);
        border: 2px solid #2196f3;
        color: #64b5f6;
    }

    @keyframes pulse {
        0%, 100% { opacity: 1; }
        50% { opacity: 0.7; }
    }

    .live-words {
        background: rgba(76, 175, 80, 0.1);
        border: 2px solid #4caf50;
        border-radius: 12px;
        padding: 20px;
        min-height: 100px;
        margin-bottom: 20px;
        font-size: 1.1rem;
        line-height: 1.6;
        color: #81c784;
    }

    .transcript-content {
        background: rgba(55, 71, 79, 0.4);
        border-radius: 12px;
        padding: 20px;
        margin-bottom: 25px;
        line-height: 1.8;
        font-size: 1rem;
        max-height: 300px;
        overflow-y: auto;
        border: 1px solid #546e7a;
    }

    .conversation-timeline {
        background: rgba(55, 71, 79, 0.4);
        border-radius: 12px;
        padding: 20px;
        margin-bottom: 25px;
        border: 1px solid #546e7a;
    }

    .speaker-entry {
        margin-bottom: 15px;
        padding: 12px;
        border-radius: 8px;
        border-left: 4px solid;
    }

    .speaker-doctor {
        background: rgba(33, 150, 243, 0.1);
        border-left-color: #2196f3;
    }

    .speaker-patient {
        background: rgba(76, 175, 80, 0.1);
        border-left-color: #4caf50;
    }

    .speaker-label {
        font-weight: 600;
        font-size: 0.9rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        margin-bottom: 5px;
    }

    .speaker-doctor .speaker-label {
        color: #64b5f6;
    }

    .speaker-patient .speaker-label {
        color: #81c784;
    }

    .speaker-text {
        color: #e0e0e0;
        line-height: 1.6;
    }

    .generate-summary-btn {
        background: linear-gradient(135deg, #2196f3 0%, #42a5f5 100%);
        color: white;
        padding: 15px 30px;
        border: none;
        border-radius: 12px;
        font-size: 1.1rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        width: 100%;
        margin-top: 20px;
        box-shadow: 0 4px 15px rgba(33, 150, 243, 0.3);
    }

    .generate-summary-btn:hover:not(:disabled) {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(33, 150, 243, 0.4);
    }

    .generate-summary-btn:disabled {
        opacity: 0.5;
        cursor: not-allowed;
        transform: none !important;
    }

    .summary-content {
        background: rgba(103, 58, 183, 0.1);
        border: 2px solid #673ab7;
        border-radius: 12px;
        padding: 20px;
        margin-top: 20px;
        line-height: 1.8;
        color: #b39ddb;
    }

    /* Scrollbar Styling */
    ::-webkit-scrollbar {
        width: 8px;
    }

    ::-webkit-scrollbar-track {
        background: rgba(55, 71, 79, 0.3);
        border-radius: 4px;
    }

    ::-webkit-scrollbar-thumb {
        background: rgba(144, 202, 249, 0.5);
        border-radius: 4px;
    }

    ::-webkit-scrollbar-thumb:hover {
        background: rgba(144, 202, 249, 0.7);
    }
</style>
</head>
<body>
    <div class="container">
        <div class="left-panel">
            <div class="section-title">🎙️ Start Recording</div>
            
            <div class="recording-controls">
                <div id="status" class="status-indicator status-ready">Ready to Record</div>
                
                <div class="button-group">
                    <button id="startBtn" class="btn btn-start" onclick="startRecording()">Start</button>
                    <button id="stopBtn" class="btn btn-stop" onclick="stopRecording()" disabled>Stop</button>
                </div>
            </div>
        </div>

        <div class="right-panel">
            <div class="section-title">🟢 Live Words</div>
            <div id="liveWords" class="live-words">Waiting for speech...</div>

            <div id="results"></div>
        </div>
    </div>

<script>
let mediaRecorder;
let audioChunks = [];
let recognition;
let isRecording = false;

// Initialize speech recognition
if ('webkitSpeechRecognition' in window) {
    recognition = new webkitSpeechRecognition();
    recognition.continuous = true;
    recognition.interimResults = true;
    recognition.lang = 'en-US';
    
    recognition.onresult = function(event) {
        let interimTranscript = '';
        let finalTranscript = '';
        
        for (let i = event.resultIndex; i < event.results.length; i++) {
            const transcript = event.results[i][0].transcript;
            if (event.results[i].isFinal) {
                finalTranscript += transcript;
            } else {
                interimTranscript += transcript;
            }
        }
        
        document.getElementById('liveWords').textContent = 
            finalTranscript + (interimTranscript ? ' ' + interimTranscript : '');
    };
    
    recognition.onerror = function(event) {
        console.error('Speech recognition error:', event.error);
    };
}

async function startRecording() {
    try {
        const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
        
        mediaRecorder = new MediaRecorder(stream);
        audioChunks = [];
        
        mediaRecorder.ondataavailable = event => {
            audioChunks.push(event.data);
        };
        
        mediaRecorder.onstop = async () => {
            const audioBlob = new Blob(audioChunks, { type: 'audio/wav' });
            await processAudio(audioBlob);
        };
        
        mediaRecorder.start();
        isRecording = true;
        
        // Start speech recognition
        if (recognition) {
            recognition.start();
        }
        
        // Update UI
        document.getElementById('startBtn').disabled = true;
        document.getElementById('stopBtn').disabled = false;
        document.getElementById('status').className = 'status-indicator status-recording';
        document.getElementById('status').textContent = '🔴 Recording...';
        
    } catch (error) {
        console.error('Error starting recording:', error);
        alert('Error accessing microphone. Please check permissions.');
    }
}

function stopRecording() {
    if (mediaRecorder && isRecording) {
        mediaRecorder.stop();
        mediaRecorder.stream.getTracks().forEach(track => track.stop());
        isRecording = false;
        
        // Stop speech recognition
        if (recognition) {
            recognition.stop();
        }
        
        // Update UI
        document.getElementById('startBtn').disabled = false;
        document.getElementById('stopBtn').disabled = true;
        document.getElementById('status').className = 'status-indicator status-processing';
        document.getElementById('status').textContent = '⏳ Processing...';
    }
}

async function processAudio(audioBlob) {
    try {
        const formData = new FormData();
        formData.append('audio', audioBlob, 'recording.wav');
        
        const response = await fetch('http://localhost:8000/analyze-conversation', {
            method: 'POST',
            body: formData
        });
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const data = await response.json();
        displayResults(data);
        
        document.getElementById('status').className = 'status-indicator status-done';
        document.getElementById('status').textContent = '✅ Done';
        
    } catch (error) {
        console.error('Error processing audio:', error);
        document.getElementById('status').className = 'status-indicator status-ready';
        document.getElementById('status').textContent = '❌ Error - Ready to try again';
        alert('Error processing audio. Please check if the backend server is running.');
    }
}

function displayResults(data) {
    let html = ``;

    if (data.full_conversation) {
        html += `<div class="section-title">👥 Conversation Timeline</div>
                 <div class="conversation-timeline">`;
        
        data.full_conversation.forEach(entry => {
            const speakerClass = entry.speaker === 'doctor' ? 'speaker-doctor' : 'speaker-patient';
            html += `
                <div class="speaker-entry ${speakerClass}">
                    <div class="speaker-label">${entry.speaker}</div>
                    <div class="speaker-text">${entry.text}</div>
                </div>
            `;
        });
        
        html += `</div>`;
        
        html += `<button class="generate-summary-btn" onclick="generateSummary()">
                    🧠 Generate Summary
                 </button>`;
    }

    document.getElementById('results').innerHTML = html;
}

async function generateSummary() {
    try {
        const button = document.querySelector('.generate-summary-btn');
        button.disabled = true;
        button.textContent = '⏳ Generating Summary...';
        
        const response = await fetch('http://localhost:8000/generate-summary', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                conversation: document.querySelector('.conversation-timeline').textContent
            })
        });
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const data = await response.json();
        
        const summaryHtml = `
            <div class="section-title">📋 AI Summary</div>
            <div class="summary-content">${data.summary}</div>
        `;
        
        document.getElementById('results').innerHTML += summaryHtml;
        
        button.disabled = false;
        button.textContent = '🧠 Generate Summary';
        
    } catch (error) {
        console.error('Error generating summary:', error);
        alert('Error generating summary. Please try again.');
        
        const button = document.querySelector('.generate-summary-btn');
        button.disabled = false;
        button.textContent = '🧠 Generate Summary';
    }
}
</script>
</body>
</html>
""", height=800)

import subprocess
import sys
import time
import os

def run_app():
    """Run the medical voice analyzer app"""
    print("🩺 Starting Medical Voice Analyzer...")
    print("=" * 50)
    
    # Check if we're in the right directory
    if not os.path.exists("frontend/app.py"):
        print("❌ Error: frontend/app.py not found!")
        print("Please run this script from the project root directory.")
        return
    
    if not os.path.exists("backend/main.py"):
        print("❌ Error: backend/main.py not found!")
        print("Please run this script from the project root directory.")
        return
    
    try:
        # Start backend server
        print("🚀 Starting backend server...")
        backend_process = subprocess.Popen([
            sys.executable, "-m", "uvicorn", "backend.main:app", 
            "--host", "0.0.0.0", "--port", "8000", "--reload"
        ])
        
        # Wait a moment for backend to start
        time.sleep(3)
        
        # Start frontend
        print("🎨 Starting frontend...")
        frontend_process = subprocess.Popen([
            sys.executable, "-m", "streamlit", "run", "frontend/app.py", 
            "--server.port", "8501", "--server.address", "0.0.0.0"
        ])
        
        print("\n✅ App started successfully!")
        print("📱 Frontend: http://localhost:8501")
        print("🔧 Backend: http://localhost:8000")
        print("\nPress Ctrl+C to stop the app")
        
        # Wait for processes
        try:
            backend_process.wait()
            frontend_process.wait()
        except KeyboardInterrupt:
            print("\n🛑 Stopping app...")
            backend_process.terminate()
            frontend_process.terminate()
            print("✅ App stopped successfully!")
            
    except Exception as e:
        print(f"❌ Error starting app: {e}")

if __name__ == "__main__":
    run_app()
